const http = require('http');
const url = require('url');
const querystring = require('querystring');

const PORT = 3000;
const REDIRECT_URI = `http://localhost:${PORT}/callback`;

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    
    if (parsedUrl.pathname === '/callback') {
        const query = parsedUrl.query;
        
        // Set CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
        
        if (query.code) {
            // Success - we got the authorization code
            console.log('\n✅ SUCCESS! Authorization code received:');
            console.log('Code:', query.code);
            if (query.state) {
                console.log('State:', query.state);
            }
            
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>OAuth Success</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        .success { color: green; }
                        .code { background: #f0f0f0; padding: 10px; margin: 20px; word-break: break-all; }
                    </style>
                </head>
                <body>
                    <h1 class="success">✅ Authorization Successful!</h1>
                    <p>You can close this window now.</p>
                    <div class="code">
                        <strong>Authorization Code:</strong><br>
                        ${query.code}
                    </div>
                    <p>Check your terminal for the full details.</p>
                </body>
                </html>
            `);
            
            // Optionally shut down server after receiving code
            setTimeout(() => {
                console.log('\n🔄 Server shutting down...');
                server.close();
            }, 2000);
            
        } else if (query.error) {
            // Error occurred
            console.log('\n❌ ERROR during OAuth:');
            console.log('Error:', query.error);
            if (query.error_description) {
                console.log('Description:', query.error_description);
            }
            
            res.writeHead(400, { 'Content-Type': 'text/html' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>OAuth Error</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        .error { color: red; }
                    </style>
                </head>
                <body>
                    <h1 class="error">❌ Authorization Failed</h1>
                    <p><strong>Error:</strong> ${query.error}</p>
                    ${query.error_description ? `<p><strong>Description:</strong> ${query.error_description}</p>` : ''}
                    <p>You can close this window and try again.</p>
                </body>
                </html>
            `);
        } else {
            // No code or error - unexpected
            res.writeHead(400, { 'Content-Type': 'text/html' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>OAuth - No Data</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    </style>
                </head>
                <body>
                    <h1>No authorization data received</h1>
                    <p>You can close this window and try again.</p>
                </body>
                </html>
            `);
        }
    } else if (parsedUrl.pathname === '/') {
        // Root path - show instructions
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>OAuth Redirect Server</title>
                <style>
                    body { font-family: Arial, sans-serif; padding: 50px; max-width: 800px; margin: 0 auto; }
                    .code { background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; }
                    .highlight { background: yellow; }
                </style>
            </head>
            <body>
                <h1>OAuth Redirect Server</h1>
                <p>This server is running and ready to receive OAuth redirects from Outlook.</p>
                
                <h2>Setup Instructions:</h2>
                <ol>
                    <li>Use this redirect URI in your Outlook OAuth app registration:</li>
                    <div class="code"><strong>${REDIRECT_URI}</strong></div>
                    
                    <li>When you initiate the OAuth flow, the user will be redirected back here after authorization.</li>
                    
                    <li>The authorization code will be displayed both in this browser and in your terminal.</li>
                </ol>
                
                <h2>Server Status:</h2>
                <p>✅ Server is running on port ${PORT}</p>
                <p>🔗 Redirect URI: <span class="highlight">${REDIRECT_URI}</span></p>
                
                <p><em>Keep this server running while performing OAuth flow.</em></p>
            </body>
            </html>
        `);
    } else {
        // 404 for other paths
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('Not Found');
    }
});

server.listen(PORT, 'localhost', () => {
    console.log(`🚀 OAuth redirect server started!`);
    console.log(`📍 Server running at: http://localhost:${PORT}`);
    console.log(`🔗 Redirect URI: ${REDIRECT_URI}`);
    console.log(`\n📋 Use this redirect URI in your Outlook OAuth app:`);
    console.log(`   ${REDIRECT_URI}`);
    console.log(`\n⏳ Waiting for OAuth redirect...`);
    console.log(`💡 Visit http://localhost:${PORT} for setup instructions`);
});

server.on('close', () => {
    console.log('✅ Server stopped.');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT. Shutting down gracefully...');
    server.close();
});
