const https = require('https');
const querystring = require('querystring');

// Configuration
const CONFIG = {
    tenantId: '76e77e1d-aa74-4939-a772-d4f4b66bc2ca',
    clientId: '3b849169-df8c-4e41-bdb4-75537f0875e1',
    scope: 'offline_access Mail.Send Mail.Read email openid profile User.Read',
    redirectUri: 'http://localhost:3000/callback'
};

// Your current refresh token (clean, single line)
const REFRESH_TOKEN = '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

function makeTokenRequest(data) {
    return new Promise((resolve, reject) => {
        // Manually URL encode to ensure proper formatting
        const postData = Object.keys(data)
            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
            .join('&');

        console.log('🔍 Request data length:', postData.length);
        console.log('🔍 Refresh token length:', data.refresh_token ? data.refresh_token.length : 'N/A');

        const options = {
            hostname: 'login.microsoftonline.com',
            port: 443,
            path: `/${CONFIG.tenantId}/oauth2/v2.0/token`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = https.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(responseData);
                    if (res.statusCode === 200) {
                        resolve(parsed);
                    } else {
                        reject(new Error(`HTTP ${res.statusCode}: ${parsed.error_description || parsed.error}`));
                    }
                } catch (e) {
                    reject(new Error(`Failed to parse response: ${responseData}`));
                }
            });
        });

        req.on('error', (e) => {
            reject(e);
        });

        req.write(postData);
        req.end();
    });
}

async function refreshAccessToken(refreshToken = REFRESH_TOKEN) {
    console.log('🔄 Refreshing access token...');

    // Try minimal required parameters first
    const data = {
        client_id: CONFIG.clientId,
        refresh_token: refreshToken,
        grant_type: 'refresh_token'
    };

    console.log('🔍 Using minimal parameters (no scope)...');

    try {
        const response = await makeTokenRequest(data);
        
        console.log('✅ Success! New tokens received:');
        console.log('📧 Access Token (for API calls):', response.access_token.substring(0, 50) + '...');
        console.log('🔄 Refresh Token (save this):', response.refresh_token ? response.refresh_token.substring(0, 50) + '...' : 'Not provided');
        console.log('⏰ Expires in:', response.expires_in, 'seconds');
        console.log('🎯 Scope:', response.scope);
        
        // Save tokens to a file for your AI application
        const tokenData = {
            access_token: response.access_token,
            refresh_token: response.refresh_token || refreshToken,
            expires_in: response.expires_in,
            expires_at: new Date(Date.now() + (response.expires_in * 1000)).toISOString(),
            scope: response.scope,
            token_type: response.token_type
        };
        
        require('fs').writeFileSync('tokens.json', JSON.stringify(tokenData, null, 2));
        console.log('💾 Tokens saved to tokens.json');
        
        return response;
        
    } catch (error) {
        console.error('❌ Error refreshing token:', error.message);
        throw error;
    }
}

async function exchangeCodeForTokens(authCode) {
    console.log('🔄 Exchanging authorization code for tokens...');
    
    const data = {
        client_id: CONFIG.clientId,
        scope: CONFIG.scope,
        code: authCode,
        redirect_uri: CONFIG.redirectUri,
        grant_type: 'authorization_code'
    };

    try {
        const response = await makeTokenRequest(data);
        
        console.log('✅ Success! Initial tokens received:');
        console.log('📧 Access Token:', response.access_token.substring(0, 50) + '...');
        console.log('🔄 Refresh Token:', response.refresh_token.substring(0, 50) + '...');
        console.log('⏰ Expires in:', response.expires_in, 'seconds');
        
        // Save tokens
        const tokenData = {
            access_token: response.access_token,
            refresh_token: response.refresh_token,
            expires_in: response.expires_in,
            expires_at: new Date(Date.now() + (response.expires_in * 1000)).toISOString(),
            scope: response.scope,
            token_type: response.token_type
        };
        
        require('fs').writeFileSync('tokens.json', JSON.stringify(tokenData, null, 2));
        console.log('💾 Tokens saved to tokens.json');
        
        return response;
        
    } catch (error) {
        console.error('❌ Error exchanging code:', error.message);
        throw error;
    }
}

// Command line usage
const command = process.argv[2];
const param = process.argv[3];

if (command === 'refresh') {
    refreshAccessToken(param)
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
} else if (command === 'exchange') {
    if (!param) {
        console.error('❌ Please provide authorization code: node token-manager.js exchange YOUR_CODE');
        process.exit(1);
    }
    exchangeCodeForTokens(param)
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
} else {
    console.log('🎯 Token Manager for Outlook OAuth');
    console.log('');
    console.log('Usage:');
    console.log('  node token-manager.js refresh [refresh_token]  - Refresh access token');
    console.log('  node token-manager.js exchange <auth_code>     - Exchange auth code for tokens');
    console.log('');
    console.log('Examples:');
    console.log('  node token-manager.js refresh                  - Use built-in refresh token');
    console.log('  node token-manager.js exchange ABC123...       - Exchange authorization code');
    console.log('');
    console.log('💡 Tokens will be saved to tokens.json for your AI application');
}
